
import { useRef, useCallback, useState } from 'react';
import {
    Box,
    styled,
    tableIcons,
    tableStyles,
    Loading,
    AGGridTablePagination,
    NoRowsOverlay,
    RichtextCellRenderer,
    BooleanRenderer,
} from 'ui-style';
import { buildSortParams, DEFAULT_TABLE_PAGINATION_SIZE, buildQueryBasedOnFilter } from 'ui-common';
import { fetch, schemaUrls } from '@tripudiotech/admin-api';
import { AgGridReact } from '@ag-grid-community/react';
import { GridReadyEvent, IServerSideGetRowsParams, IServerSideGetRowsRequest, GridOptions } from '@ag-grid-community/core';
import { Outlet, useNavigate } from 'react-router-dom';

const ContentWrapper = styled(Box)(() => ({
    ...tableStyles,
    height: '100%',
    width: '100%',
    '& .handle': {
        position: 'absolute',
        height: '100%',
        width: '8px',
        right: 0,
        top: 0,
    },
}));

const gridOptions: GridOptions = {
    headerHeight: 34,
    rowHeight: 34,
    loadingOverlayComponent: Loading,
    animateRows: true,
    defaultColDef: {
        sortable: true,
        resizable: true,
        flex: 1,
        filter: true,
        floatingFilter: false,
        cellStyle: () => ({
            display: 'block',
        }),
    },
    getRowId: (params) => {
        return params.data.id;
    },
    columnDefs: [
        {
            field: 'name',
            headerName: 'Name',
            filter: 'agTextColumnFilter',
            flex: 1,
        },
        {
            field: 'displayName',
            headerName: 'Display Name',
            filter: 'agTextColumnFilter',
            flex: 1,
        },
        {
            field: 'type',
            headerName: 'Type',
            filter: 'agTextColumnFilter',
            flex: 1,
        },
        {
            field: 'description',
            headerName: 'Description',
            filter: 'agTextColumnFilter',
            cellRenderer: RichtextCellRenderer,
        },
        {
            field: 'system',
            headerName: 'System',
            cellRenderer: BooleanRenderer,
        },
        {
            field: 'nullable',
            headerName: 'Can Be Empty',
            cellRenderer: BooleanRenderer,
        },
    ],
    icons: tableIcons,
    cacheBlockSize: DEFAULT_TABLE_PAGINATION_SIZE,
    rowModelType: 'serverSide',
    serverSideInfiniteScroll: true,
    suppressRowClickSelection: true,
    suppressPaginationPanel: true,
    pagination: true,
}

const AttributeListView = () => {
    const navigate = useNavigate();
    const [totalRows, setTotalRows] = useState<number>(1);
    const gridRef = useRef<AgGridReact>(null);

    const createServerSideDataSource = useCallback((event: GridReadyEvent) => {
        const buildParams = (params: IServerSideGetRowsRequest) => {
            const filterModel = params.filterModel;
            let queryParams: any = {
                offset: params.startRow || 0,
                limit: event?.api.paginationGetPageSize() || DEFAULT_TABLE_PAGINATION_SIZE,
                ...buildSortParams(params.sortModel),
            };
            if (filterModel && Object.keys(filterModel).length > 0) {
                const filterConditions: any[] = [];
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            }
            return queryParams;
        };
        return {
            getRows: (params: IServerSideGetRowsParams) => {
                gridRef.current?.api.showLoadingOverlay();
                fetch({
                    ...schemaUrls.getListAttribute,
                    qs: buildParams(params?.request),
                })
                    .then((response) => {
                        if (response.status !== 200) {
                            params.failCallback();
                            return;
                        }
                        const rowsThisPage = response.data.data;
                        const rowCount = response.data.pageInfo.total;
                        params.success({ rowData: rowsThisPage, rowCount });
                        setTotalRows(rowCount);
                    })
                    .finally(() => {
                        gridRef.current?.api.hideOverlay();
                        if (gridRef.current?.api.getDisplayedRowCount() === 0) {
                            gridRef.current?.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, []);

    return (
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <ContentWrapper>
                <Box className="ag-theme-alpine" sx={{ height: '100%' }}>
                    <AgGridReact
                        {...gridOptions}
                        ref={gridRef}
                        onGridReady={(params: GridReadyEvent) => {
                            const dataSource = createServerSideDataSource(params);
                            params.api.setServerSideDatasource(dataSource);
                        }}
                        noRowsOverlayComponent={NoRowsOverlay}
                        noRowsOverlayComponentParams={{
                            message: 'No attributes found',
                        }}
                        onRowClicked={(params) => {
                            const { data } = params;
                            navigate(`/attribute/${data.id}`, { state: { data } });
                        }}
                    />
                </Box>
            </ContentWrapper>
            <AGGridTablePagination
                gridRef={gridRef}
                totalRows={totalRows}
                pageSize={DEFAULT_TABLE_PAGINATION_SIZE}
            />
            <Outlet />
        </Box>
    );
};

export default AttributeListView;