import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
    RightTray,
    Box,
    LoadingOverlay,
} from 'ui-style';
import AttributeInformation from '../../components/attribute/AttributeInformation';
import useAttributeStore from '../../store/useAttributeStore';

const AttributeDetailView = () => {
    const { attributeId } = useParams();
    const { attributeDetail, isLoading, getAttributeDetail } = useAttributeStore()
    const navigate = useNavigate();

    useEffect(() => {
        if (attributeId) {
            getAttributeDetail(attributeId);
        }
    }, [attributeId]);

    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <RightTray
                title={`Attribute ${attributeDetail?.name || ''}`}
                open={true}
                onClose={() => {
                    navigate('/attribute');
                }}
                hideConfirm
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    {attributeDetail && (
                        <AttributeInformation
                            attributeDetail={attributeDetail}
                            schemas={attributeDetail.schemas || []}
                        />
                    )}
                </Box>
            </RightTray>
            {isLoading && <LoadingOverlay />}
        </Box>
    );
};

export default AttributeDetailView;
