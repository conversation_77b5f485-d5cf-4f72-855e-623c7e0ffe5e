/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useEffect, useMemo } from 'react';
import {
    Box,
    FormikCheckBox,
    FormikTextField,
    MenuItem,
    Typography,
    AccordionSummary,
    ExpandMoreIcon,
    BaseAccordion,
    AccordionDetails,
    Button,
    DeleteIcon,
    Grow,
    Loading,
} from 'ui-style';
import * as yup from 'yup';
import { Formik, Form, Field } from 'formik';
import capitalize from 'lodash/capitalize';
import startCase from 'lodash/startCase';
import { RelationTypeEvent, RelationTypeEventLabel, RELATION_FLOAT_TYPE, RELATION_SIDE_TYPE } from 'ui-common';
import { Group } from '../advanced-search/FilterComponents';
import { useSchemaDetail } from '@tripudiotech/admin-caching-store';
import { INITIAL_CONDITION } from '../../constants';
import { QUERY } from '@tripudiotech/admin-api';
import { convertSearchCriteriaToFormValues, extractSearchCriteriaWithoutType } from '../../utils/advancedSearchHelper';

const DEFAULT_EVENT_VALUE = {
    type: null,
    floatStateCondition: null,
    floatType: null,
};

const filterTypeSchema = yup
    .string()
    .oneOf(['attribute', 'lifecycleState', 'relation', 'classification', 'relationExists', 'relationAttribute'])
    .required('Filter type is required');

export const filterSchema = yup.object({
    operator: yup.string().required('Operator is required'),
    values: yup.mixed().when('operator', {
        is: (operator) => operator !== QUERY.IS_NULL && operator !== QUERY.IS_NON_NULL,
        then: yup
            .object({
                field: yup.string().required('Field is required'),
                value: yup.mixed().required('Value is required'),
            })
            .required('Value is required'),
        otherwise: yup.mixed().notRequired(),
    }),
    filterType: filterTypeSchema,
});

export const conditionSchema = yup.lazy((value) => {
    if (value?.filterType === 'group' || value?.filterType === 'relation') {
        return groupSchema;
    }
    if (value?.filterType === 'classification') {
        return classificationSchema;
    }
    if (value?.filterType === 'relationExists') {
        return yup.mixed().notRequired();
    }
    return filterSchema;
});
// Schema for group filters (recursive)
export const groupSchema = yup.object({
    operator: yup.string().oneOf([QUERY.AND, QUERY.OR]).required('Operator is required'),
    values: yup
        .array()
        .of(conditionSchema)
        .min(1, 'Group Filter must have at least one value')
        .required('Values are required'),
    filterType: yup.string().oneOf(['group', 'relation']).required('Filter type is required'),
});

export const classificationSchema = yup.object({
    operator: yup.string().required('Operator is required'),
    values: yup.mixed().when('operator', {
        is: (operator) => operator !== QUERY.IS_NULL && operator !== QUERY.IS_NON_NULL,
        then: yup
            .object({
                field: yup.string().required('Field is required'),
                value: yup.mixed().required('Value is required'),
                classification: yup.string().required('Please select a classification'),
            })
            .required('Value is required'),
        otherwise: yup.mixed().notRequired(),
    }),
    filterType: filterTypeSchema,
});
const validationSchema = yup.object().shape({
    name: yup
        .string()
        .required('Name is required')
        .matches(RegExp('^[A-Z]+(?:_[A-Z]+)*$'), 'Relation Name only allow uppercase, underscore characters'),
    displayName: yup.string().required('Display Name is required'),
    toEntityTypeNames: yup.array().min(1, 'To Entity Type is required'),
    isVisible: yup.boolean(),
    isRequired: yup.boolean(),
    isSingleRelation: yup.boolean(),
    withSearchCriteria: yup.boolean(),
    condition: yup.mixed().when('withSearchCriteria', {
        is: true,
        then: yup
            .object({
                operator: yup.string().oneOf([QUERY.AND, QUERY.OR]).required('Operator is required'),
                values: yup
                    .array()
                    .of(conditionSchema)
                    .min(1, 'Group Filter must have at least one element')
                    .required('Values is required'),
            })
            .required('Condition is required'),
        otherwise: yup.mixed().notRequired(),
    }),
});

const RelationForm = ({ selectedTypes, formRef, handleSubmit, defaultInitialValues = null }) => {
    const [schemaDetail, getSchema] = useSchemaDetail((state) => [state.schema[selectedTypes?.[0]], state.getSchema]);
    const initialValues = useMemo(() => {
        return {
            name: defaultInitialValues?.name || '',
            displayName: defaultInitialValues?.displayName || '',
            description: defaultInitialValues?.description || '',
            toEntityTypes: selectedTypes,
            isRequired: defaultInitialValues?.required || false,
            isVisible: defaultInitialValues?.visible || false,
            isSingleRelation: defaultInitialValues ? Boolean(defaultInitialValues.singleRelation) : true,
            onFromSideRevise: defaultInitialValues?.onFromSideRevise || {
                ...DEFAULT_EVENT_VALUE,
            },
            onFromSideClone: defaultInitialValues?.onFromSideClone || {
                ...DEFAULT_EVENT_VALUE,
            },
            onToSideRevise: defaultInitialValues?.onToSideRevise || {
                ...DEFAULT_EVENT_VALUE,
            },
            onToSideClone: defaultInitialValues?.onToSideClone || {
                ...DEFAULT_EVENT_VALUE,
            },
            withSearchCriteria: defaultInitialValues
                ? Boolean(defaultInitialValues?.constraint?.searchCriteria)
                : false,
            condition: Boolean(defaultInitialValues?.constraint?.searchCriteria)
                ? convertSearchCriteriaToFormValues(
                      extractSearchCriteriaWithoutType(defaultInitialValues.constraint.searchCriteria)
                  )
                : INITIAL_CONDITION,
        };
    }, [selectedTypes, defaultInitialValues]);

    useEffect(() => {
        if (selectedTypes?.length === 1 && !schemaDetail) {
            getSchema(selectedTypes[0]);
        }
    }, [selectedTypes, schemaDetail]);

    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px', padding: '16px 24px', overflow: 'auto' }}>
            <Formik
                enableReinitialize
                initialValues={initialValues}
                innerRef={formRef}
                validationSchema={validationSchema}
                onSubmit={(values, { setSubmitting }) => {
                    handleSubmit(values as any);
                    setSubmitting(false);
                }}
            >
                {(formProps) => {
                    const { values, setFieldValue } = formProps;
                    return (
                        <Form id="metadata-form" style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                            <Typography
                                variant="label2-med"
                                sx={{
                                    color: (theme) => theme.palette.info.main,
                                }}
                            >
                                General Information
                            </Typography>
                            <Field
                                fullWidth
                                component={FormikTextField}
                                label="Name"
                                name="name"
                                variant="outlined"
                                InputProps={{
                                    readOnly: Boolean(defaultInitialValues),
                                }}
                            />
                            <Field
                                fullWidth
                                component={FormikTextField}
                                label="Description"
                                multiline
                                minRows={2}
                                name="description"
                                variant="outlined"
                            />
                            <Field
                                fullWidth
                                component={FormikTextField}
                                label="Display Name"
                                name="displayName"
                                variant="outlined"
                            />
                            <Field
                                fullWidth
                                options={selectedTypes}
                                component={FormikTextField}
                                label="To Entity Type"
                                name="toEntityTypes"
                                variant="outlined"
                                value={selectedTypes?.join(', ')}
                                multiple
                                readOnly
                                disabled
                            />
                            <Field
                                fullWidth
                                component={FormikCheckBox}
                                type="checkbox"
                                name="isRequired"
                                Label={{ label: 'Required' }}
                            />
                            <Field
                                fullWidth
                                component={FormikCheckBox}
                                type="checkbox"
                                name="isVisible"
                                Label={{ label: 'Visible in UI' }}
                            />
                            <Field
                                fullWidth
                                component={FormikCheckBox}
                                type="checkbox"
                                name="isSingleRelation"
                                Label={{ label: 'Prevent Duplicate Relation' }}
                            />
                            <Field
                                fullWidth
                                component={FormikCheckBox}
                                type="checkbox"
                                name="withSearchCriteria"
                                Label={{ label: 'Search Criteria' }}
                            />
                            <Grow in={values.withSearchCriteria} unmountOnExit>
                                <Box>
                                    {schemaDetail ? (
                                        <>
                                            <Typography
                                                variant="label2-med"
                                                sx={{
                                                    color: (theme) => theme.palette.info.main,
                                                }}
                                            >
                                                Search Criteria
                                            </Typography>
                                            <Box className="query-container">
                                                <Group
                                                    name={'condition'}
                                                    schemaDetail={schemaDetail}
                                                    formProps={formProps}
                                                    sx={{
                                                        overflow: 'auto',
                                                        maxHeight: 'calc(100vh - 468px)',
                                                        background: '#FFFFFF',
                                                        '& .MuiOutlinedInput-root': {
                                                            width: 'max-content',
                                                        },
                                                    }}
                                                />
                                            </Box>
                                        </>
                                    ) : (
                                        <Loading />
                                    )}
                                </Box>
                            </Grow>
                            <Typography
                                variant="label2-med"
                                sx={{
                                    color: (theme) => theme.palette.info.main,
                                }}
                            >
                                Event
                            </Typography>
                            {Object.values(RelationTypeEvent).map((relTypeEvent: string) => {
                                return (
                                    <BaseAccordion
                                        key={relTypeEvent}
                                        TransitionProps={{ unmountOnExit: true }}
                                        sx={{ '&.Mui-expanded': { margin: 0 } }}
                                    >
                                        <AccordionSummary
                                            className="summary"
                                            expandIcon={
                                                <Box
                                                    sx={{
                                                        height: '40px',
                                                        width: '40px',
                                                        display: 'flex',
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                    }}
                                                    className="allowPointer"
                                                >
                                                    <ExpandMoreIcon sx={{ width: '16px', height: '16px' }} />
                                                </Box>
                                            }
                                        >
                                            <Box
                                                sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}
                                            >
                                                <Typography variant="label2-med">
                                                    {RelationTypeEventLabel[relTypeEvent]}
                                                </Typography>

                                                {values[relTypeEvent].type && (
                                                    <Button
                                                        variant="outlined"
                                                        color="error"
                                                        size="xs"
                                                        endIcon={<DeleteIcon />}
                                                        onClick={() => {
                                                            setFieldValue(`${relTypeEvent}.type`, null);
                                                            setFieldValue(`${relTypeEvent}.floatType`, null);
                                                            setFieldValue(`${relTypeEvent}.floatStateCondition`, null);
                                                        }}
                                                    >
                                                        Clear
                                                    </Button>
                                                )}
                                            </Box>
                                        </AccordionSummary>
                                        <AccordionDetails className="accordionDetails">
                                            <Box
                                                sx={{
                                                    mt: '16px',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    gap: '12px',
                                                }}
                                            >
                                                <Field
                                                    fullWidth
                                                    select
                                                    type="text"
                                                    name={`${relTypeEvent}.type`}
                                                    label="Relation Side Type"
                                                    component={FormikTextField}
                                                    onChange={(e: any) => {
                                                        const value = e.target.value;
                                                        setFieldValue(`${relTypeEvent}.type`, value);
                                                        if (value !== RELATION_SIDE_TYPE.FLOAT) {
                                                            setFieldValue(`${relTypeEvent}.floatType`, null);
                                                            setFieldValue(`${relTypeEvent}.floatStateCondition`, null);
                                                        }
                                                    }}
                                                >
                                                    {Object.values(RELATION_SIDE_TYPE).map((sideType) => (
                                                        <MenuItem value={sideType} key={sideType}>
                                                            {capitalize(sideType)}
                                                        </MenuItem>
                                                    ))}
                                                </Field>
                                                {values[relTypeEvent].type === RELATION_SIDE_TYPE.FLOAT && (
                                                    <Field
                                                        fullWidth
                                                        select
                                                        type="text"
                                                        name={`${relTypeEvent}.floatType`}
                                                        label="Float Type"
                                                        component={FormikTextField}
                                                    >
                                                        {Object.values(RELATION_FLOAT_TYPE).map((floatType) => (
                                                            <MenuItem value={floatType} key={floatType}>
                                                                {capitalize(startCase(floatType))}
                                                            </MenuItem>
                                                        ))}
                                                    </Field>
                                                )}
                                                {values[relTypeEvent].floatType === RELATION_FLOAT_TYPE.STATE_BASE && (
                                                    <Field
                                                        fullWidth
                                                        type="text"
                                                        name={`${relTypeEvent}.floatStateCondition`}
                                                        label="Float State Condition"
                                                        component={FormikTextField}
                                                    />
                                                )}
                                            </Box>
                                        </AccordionDetails>
                                    </BaseAccordion>
                                );
                            })}
                        </Form>
                    );
                }}
            </Formik>
        </Box>
    );
};

export default RelationForm;
