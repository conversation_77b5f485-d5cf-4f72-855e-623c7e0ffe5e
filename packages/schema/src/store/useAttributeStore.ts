
import { create } from 'zustand';
import { AttributeDetail, fetch, schemaUrls } from "@tripudiotech/admin-api";

type AttributeStore = {
    isLoading: boolean;
    attributeDetail: AttributeDetail;
    getAttributeDetail: (attributeId: string) => Promise<void>;
}

const initialStates = {
    isLoading: false,
    attributeDetail: null,
}

const useAttributeStore = create<AttributeStore>((set, get) => ({
    ...initialStates,
    getAttributeDetail: async (attributeId: string) => {
        try {
            set({ isLoading: true });
            const response = await fetch({
                ...schemaUrls.getAttributeDetail,
                params: { attributeId },
            });
            set({ attributeDetail: response.data });
        } catch (error) {
            console.error('Error fetching attribute detail:', error);
        } finally {
            set({ isLoading: false });
        }
    }
}))

export default useAttributeStore