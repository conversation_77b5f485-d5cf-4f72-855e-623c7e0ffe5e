import { Method } from '../services/fetch';
declare const schemaUrls: {
    readonly getSchemaTree: {
        readonly method: Method.GET;
        readonly url: `${string}/schema`;
    };
    readonly getSchemaDetail: {
        readonly method: Method.GET;
        readonly url: `${string}/schema/:entityTypeName`;
    };
    readonly updateEntityType: {
        readonly method: Method.PUT;
        readonly url: `${string}/schema/:entityTypeName`;
    };
    readonly createEntityAttribute: {
        readonly method: Method.POST;
        readonly url: `${string}/schema/:entityTypeName/attribute`;
    };
    readonly updateAttribute: {
        readonly method: Method.PUT;
        readonly url: `${string}/schema/:schemaName/attribute/:attributeName`;
    };
    readonly deleteEntityAttribute: {
        readonly method: Method.DELETE;
        readonly url: `${string}/schema/:entityTypeName/attribute/:attributeName`;
    };
    readonly createEntitySubType: {
        readonly method: Method.POST;
        readonly url: `${string}/schema/:entityTypeName/schema`;
    };
    readonly getListAttribute: {
        readonly method: Method.GET;
        readonly url: `${string}/attribute`;
    };
    readonly getAttributeDetail: {
        readonly method: Method.GET;
        readonly url: `${string}/attribute/:attributeId`;
    };
    readonly deleteEntityType: {
        readonly method: Method.DELETE;
        readonly url: `${string}/schema/:entityTypeName`;
    };
    readonly fetchEntityLifecycle: {
        readonly method: Method.GET;
        readonly url: `${string}/schema/:entityTypeName/life-cycle`;
    };
    readonly updateDefaultLifecycle: {
        readonly method: Method.POST;
        readonly url: `${string}/schema/:entityTypeName/life-cycle/:lifecycleId/set-default`;
    };
    readonly getListLifeCycle: {
        readonly method: Method.GET;
        readonly url: `${string}/life-cycle`;
    };
    readonly getLifeCycle: {
        readonly method: Method.GET;
        readonly url: `${string}/life-cycle/:lifecycleId`;
    };
    readonly getLifeCycleHierarchy: {
        readonly method: Method.GET;
        readonly url: `${string}/life-cycle/:lifecycleId/hierarchy`;
    };
    readonly setNextLifeCycleHierarchy: {
        readonly method: Method.POST;
        readonly url: `${string}/life-cycle/:lifecycleId/next/:nextLifecycleId`;
    };
    readonly deleteNextLifeCycleHierarchy: {
        readonly method: Method.DELETE;
        readonly url: `${string}/life-cycle/:lifecycleId/next/:nextLifecycleId`;
    };
    readonly assignLifeCycles: {
        readonly method: Method.POST;
        readonly url: `${string}/schema/:entityTypeName/life-cycle`;
    };
    readonly unassignLifeCycles: {
        readonly method: Method.DELETE;
        readonly url: `${string}/schema/:entityTypeName/life-cycle`;
    };
    readonly deleteLifecycle: {
        readonly method: Method.DELETE;
        readonly url: `${string}/life-cycle/:id`;
    };
    readonly createLifecycle: {
        readonly method: Method.POST;
        readonly url: `${string}/life-cycle`;
    };
    readonly createRelation: {
        readonly method: Method.POST;
        readonly url: `${string}/relation`;
    };
    readonly updateLifecycle: {
        readonly method: Method.PUT;
        readonly url: `${string}/life-cycle/:lifeCycleId`;
    };
    readonly deleteRelation: {
        readonly method: Method.DELETE;
        readonly url: `${string}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName`;
    };
    readonly getRelation: {
        readonly method: Method.GET;
        readonly url: `${string}/relation/:fromEntityType/:relationType/:toEntityType`;
    };
    readonly updateRelation: {
        readonly method: Method.PUT;
        readonly url: `${string}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName`;
    };
    readonly createRelationAttribute: {
        readonly method: Method.POST;
        readonly url: `${string}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName/attribute`;
    };
    readonly updateRelationAttribute: {
        readonly method: Method.PUT;
        readonly url: `${string}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName/attribute/:attributeName`;
    };
    readonly unassignRelationAttribute: {
        readonly method: Method.DELETE;
        readonly url: `${string}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName/attribute/:attributeName`;
    };
    readonly getQuantityGroups: {
        readonly method: Method.GET;
        readonly url: `${string}/unit/quantity-kind`;
    };
    readonly getQuantityUnits: {
        readonly method: Method.GET;
        readonly url: `${string}/unit/:groupId/quantity-type`;
    };
    readonly createAttributeIdentifier: {
        readonly method: Method.POST;
        readonly url: `${string}/schema/:entityType/attribute/:attributeName/identifier`;
    };
    readonly updateAttributeIdentifier: {
        readonly method: Method.PUT;
        readonly url: `${string}/schema/:entityType/attribute/:attributeName/identifier`;
    };
    readonly deleteAttributeIdentifier: {
        readonly method: Method.DELETE;
        readonly url: `${string}/schema/:entityType/attribute/:attributeName/identifier`;
    };
    readonly getAppliedProcess: {
        readonly method: Method.GET;
        readonly url: `${string}/schema/:entityType/process`;
    };
    readonly applyClassification: {
        readonly method: Method.POST;
        readonly url: `${string}/schema/:entityType/classification`;
    };
    readonly getAccessedRoles: {
        readonly method: Method.GET;
        readonly url: `${string}/schema/:entityType/role`;
    };
    readonly assignRole: {
        readonly method: Method.POST;
        readonly url: `${string}/schema/:entityType/role/:roleId`;
    };
    readonly unassignRole: {
        readonly method: Method.DELETE;
        readonly url: `${string}/schema/:entityType/role/:roleId`;
    };
    readonly getPermissionRoles: {
        readonly method: Method.GET;
        readonly url: `${string}/permission-role`;
    };
    readonly getPermissionRoleByName: {
        readonly method: Method.GET;
        readonly url: `${string}/permission-role/:roleName`;
    };
    readonly createPermissionRole: {
        readonly method: Method.POST;
        readonly url: `${string}/permission-role`;
    };
    readonly deletePermissionRole: {
        readonly method: Method.DELETE;
        readonly url: `${string}/permission-role/:roleName`;
    };
    readonly updatePermissionRole: {
        readonly method: Method.PUT;
        readonly url: `${string}/permission-role/:roleName`;
    };
    readonly createDigitalThread: {
        readonly method: Method.POST;
        readonly url: `${string}/digital-thread`;
    };
    readonly updateDigitalThread: {
        readonly method: Method.PUT;
        readonly url: `${string}/digital-thread/:id`;
    };
    readonly deleteDigitalThread: {
        readonly method: Method.DELETE;
        readonly url: `${string}/digital-thread/:id`;
    };
    readonly getDigitalThreadById: {
        readonly method: Method.GET;
        readonly url: `${string}/digital-thread/:id`;
    };
    readonly getAllDigitalThreads: {
        readonly method: Method.GET;
        readonly url: `${string}/digital-thread`;
    };
    readonly reloadCache: {
        readonly method: Method.POST;
        readonly url: `${string}/caching/reload/all`;
    };
    readonly getSchemaClassification: {
        readonly method: Method.GET;
        readonly url: `${string}/schema/:entityType/classification`;
    };
};
export default schemaUrls;
//# sourceMappingURL=index.d.ts.map