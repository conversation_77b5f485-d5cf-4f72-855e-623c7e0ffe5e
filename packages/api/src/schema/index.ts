/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Method } from '../services/fetch';
const BASE_URL = `${process.env.BASE_URL}/schema-manager`;

const schemaUrls = {
    getSchemaTree: {
        method: Method.GET,
        url: `${BASE_URL}/schema`,
    },
    getSchemaDetail: {
        method: Method.GET,
        url: `${BASE_URL}/schema/:entityTypeName`,
    },
    updateEntityType: {
        method: Method.PUT,
        url: `${BASE_URL}/schema/:entityTypeName`,
    },
    createEntityAttribute: {
        method: Method.POST,
        url: `${BASE_URL}/schema/:entityTypeName/attribute`,
    },
    updateAttribute: {
        method: Method.PUT,
        url: `${BASE_URL}/schema/:schemaName/attribute/:attributeName`,
    },
    deleteEntityAttribute: {
        method: Method.DELETE,
        url: `${BASE_URL}/schema/:entityTypeName/attribute/:attributeName`,
    },
    createEntitySubType: {
        method: Method.POST,
        url: `${BASE_URL}/schema/:entityTypeName/schema`,
    },
    getListAttribute: {
        method: Method.GET,
        url: `${BASE_URL}/attribute`,
    },
    getAttributeDetail: {
        method: Method.GET,
        url: `${BASE_URL}/attribute/:attributeId`,
    },
    deleteEntityType: {
        method: Method.DELETE,
        url: `${BASE_URL}/schema/:entityTypeName`,
    },
    fetchEntityLifecycle: {
        method: Method.GET,
        url: `${BASE_URL}/schema/:entityTypeName/life-cycle`,
    },
    updateDefaultLifecycle: {
        method: Method.POST,
        url: `${BASE_URL}/schema/:entityTypeName/life-cycle/:lifecycleId/set-default`,
    },
    getListLifeCycle: {
        method: Method.GET,
        url: `${BASE_URL}/life-cycle`,
    },
    getLifeCycle: {
        method: Method.GET,
        url: `${BASE_URL}/life-cycle/:lifecycleId`,
    },
    getLifeCycleHierarchy: {
        method: Method.GET,
        url: `${BASE_URL}/life-cycle/:lifecycleId/hierarchy`,
    },
    setNextLifeCycleHierarchy: {
        method: Method.POST,
        url: `${BASE_URL}/life-cycle/:lifecycleId/next/:nextLifecycleId`,
    },
    deleteNextLifeCycleHierarchy: {
        method: Method.DELETE,
        url: `${BASE_URL}/life-cycle/:lifecycleId/next/:nextLifecycleId`,
    },
    assignLifeCycles: {
        method: Method.POST,
        url: `${BASE_URL}/schema/:entityTypeName/life-cycle`,
    },
    unassignLifeCycles: {
        method: Method.DELETE,
        url: `${BASE_URL}/schema/:entityTypeName/life-cycle`,
    },
    deleteLifecycle: {
        method: Method.DELETE,
        url: `${BASE_URL}/life-cycle/:id`,
    },
    createLifecycle: {
        method: Method.POST,
        url: `${BASE_URL}/life-cycle`,
    },
    createRelation: {
        method: Method.POST,
        url: `${BASE_URL}/relation`,
    },
    updateLifecycle: {
        method: Method.PUT,
        url: `${BASE_URL}/life-cycle/:lifeCycleId`,
    },
    deleteRelation: {
        method: Method.DELETE,
        url: `${BASE_URL}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName`,
    },
    getRelation: {
        method: Method.GET,
        url: `${BASE_URL}/relation/:fromEntityType/:relationType/:toEntityType`,
    },
    updateRelation: {
        method: Method.PUT,
        url: `${BASE_URL}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName`,
    },
    createRelationAttribute: {
        method: Method.POST,
        url: `${BASE_URL}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName/attribute`,
    },
    updateRelationAttribute: {
        method: Method.PUT,
        url: `${BASE_URL}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName/attribute/:attributeName`,
    },
    unassignRelationAttribute: {
        method: Method.DELETE,
        url: `${BASE_URL}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName/attribute/:attributeName`,
    },
    getQuantityGroups: {
        method: Method.GET,
        url: `${BASE_URL}/unit/quantity-kind`,
    },
    getQuantityUnits: {
        method: Method.GET,
        url: `${BASE_URL}/unit/:groupId/quantity-type`,
    },
    createAttributeIdentifier: {
        method: Method.POST,
        url: `${BASE_URL}/schema/:entityType/attribute/:attributeName/identifier`,
    },
    updateAttributeIdentifier: {
        method: Method.PUT,
        url: `${BASE_URL}/schema/:entityType/attribute/:attributeName/identifier`,
    },
    deleteAttributeIdentifier: {
        method: Method.DELETE,
        url: `${BASE_URL}/schema/:entityType/attribute/:attributeName/identifier`,
    },
    getAppliedProcess: {
        method: Method.GET,
        url: `${BASE_URL}/schema/:entityType/process`,
    },
    applyClassification: {
        method: Method.POST,
        url: `${BASE_URL}/schema/:entityType/classification`,
    },

    getAccessedRoles: {
        method: Method.GET,
        url: `${BASE_URL}/schema/:entityType/role`,
    },
    assignRole: {
        method: Method.POST,
        url: `${BASE_URL}/schema/:entityType/role/:roleId`,
    },
    unassignRole: {
        method: Method.DELETE,
        url: `${BASE_URL}/schema/:entityType/role/:roleId`,
    },
    getPermissionRoles: {
        method: Method.GET,
        url: `${BASE_URL}/permission-role`,
    },
    getPermissionRoleByName: {
        method: Method.GET,
        url: `${BASE_URL}/permission-role/:roleName`,
    },
    createPermissionRole: {
        method: Method.POST,
        url: `${BASE_URL}/permission-role`,
    },
    deletePermissionRole: {
        method: Method.DELETE,
        url: `${BASE_URL}/permission-role/:roleName`,
    },
    updatePermissionRole: {
        method: Method.PUT,
        url: `${BASE_URL}/permission-role/:roleName`,
    },
    createDigitalThread: {
        method: Method.POST,
        url: `${BASE_URL}/digital-thread`,
    },
    updateDigitalThread: {
        method: Method.PUT,
        url: `${BASE_URL}/digital-thread/:id`,
    },
    deleteDigitalThread: {
        method: Method.DELETE,
        url: `${BASE_URL}/digital-thread/:id`,
    },
    getDigitalThreadById: {
        method: Method.GET,
        url: `${BASE_URL}/digital-thread/:id`,
    },
    getAllDigitalThreads: {
        method: Method.GET,
        url: `${BASE_URL}/digital-thread`,
    },
    reloadCache: {
        method: Method.POST,
        url: `${BASE_URL}/caching/reload/all`,
    },
    getSchemaClassification: {
        method: Method.GET,
        url: `${BASE_URL}/schema/:entityType/classification`,
    },
} as const;

export default schemaUrls;
